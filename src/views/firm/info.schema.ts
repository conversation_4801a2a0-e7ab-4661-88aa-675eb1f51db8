import { DescItem } from '@/components/Description';
import { filePreview } from '@/components/RenderVnode';
import { calculateFileSize } from '@/utils/calculateFileSize';
import { FormSchema } from '@/components/Table';
import { useMapWithI18n } from '@/hooks/web/useOnlineI18n';
import { UpgradeTypeList } from '@/maps/prMaps';

// 固件基础信息
export const basicInfoSchema: DescItem[] = [
  {
    field: 'name',
    label: '固件名称',
  },
  {
    field: 'productList',
    label: '支持设备型号',
    render: (_, record) => {
      if (record.productList && record.productList.length > 0) {
        return record.productList.map((product: any) => product.model).join(', ');
      }
      return '-';
    },
  },
  {
    field: 'version',
    label: '固件版本',
  },
  {
    field: 'signType',
    label: '签名方式',
  },
  {
    field: 'sign',
    label: '签名',
  },
  {
    field: 'file',
    label: '固件文件',
    render: (val: string) => {
      return filePreview(val);
    },
  },
  {
    field: 'size',
    label: '文件大小',
    render: (val: string) => {
      return calculateFileSize(val);
    },
  },
  {
    field: 'remark',
    label: '固件描述',
  },
  {
    field: 'createTime',
    label: '创建时间',
  },
  {
    field: 'createBy',
    label: '创建人',
  },
];

// 固件升级任务创建
export const taskSchema = (): FormSchema[] => [
  {
    field: 'name',
    label: '任务名称',
    fields: ['id', 'firmwareId'],
    component: 'Input',
    required: true,
  },
  {
    field: 'updateType',
    label: '升级设备筛选', // 按照型号 合作伙伴 终端客户
    component: 'Select',
    componentProps: {
      options: useMapWithI18n(UpgradeTypeList),
    },
    required: true,
  },
  {
    field: 'productList',
    label: '选择合作伙伴',
    component: 'Select',
  },
  {
    field: 'productList',
    label: '选择终端客户',
    component: 'Select',
  },
  {
    field: 'remark', /// 适配的全部型号 选择的设备型号升级
    label: '升级设备',
    component: 'Input',
  },
  {
    field: 'file',
    label: '选择型号',
    component: 'Upload',
  },
  {
    field: 'remark',
    label: '任务描述',
    component: 'InputTextArea',
  },
];
